# PRODUCTION DATABASE INTEGRATION TEMPLATE

## REPLACE THESE VALUES WITH ACTUAL PRODUCTION DATA

### 1. DATABASE CONNECTION
```
PRODUCTION_MYSQL_HOST = "their-mysql-server.company.com"
PRODUCTION_MYSQL_PORT = "3306"
PRODUCTION_DB_USER = "debezium_user"
PRODUCTION_DB_PASSWORD = "secure_password_here"
UNIQUE_SERVER_ID = "999999"  # Must be unique across all MySQL servers
```

### 2. DATABASE & TABLES
```
PRODUCTION_DATABASE_NAMES = "zepto_orders,zepto_users,zepto_products,zepto_inventory"

PRODUCTION_TABLE_LIST = "zepto_orders.orders,zepto_orders.order_items,zepto_orders.payments,zepto_users.users,zepto_users.addresses,zepto_users.user_preferences,zepto_products.products,zepto_products.categories,zepto_products.product_variants,zepto_inventory.stock_levels"
```

### 3. SSL CONFIGURATION (if required)
```
PRODUCTION_SSL_MODE = "required"  # or "disabled" if not using SSL
```

### 4. PERFORMANCE TUNING (adjust based on their volume)
```
tasks.max = 4  # Increase for higher throughput
max.batch.size = 4096  # Larger batches for high volume
binlog.buffer.size = 32768  # Larger buffer for high TPS
```

### 5. KAFKA TOPICS THAT WILL BE CREATED
Based on table list above, these Kafka topics will be created:
- orders
- order_items  
- payments
- users
- addresses
- user_preferences
- products
- categories
- product_variants
- stock_levels

### 6. TYPESENSE COLLECTIONS
Each Kafka topic will create a corresponding Typesense collection for search.

## VALIDATION CHECKLIST
- [ ] Network connectivity tested
- [ ] Database user privileges verified
- [ ] Binary logging confirmed enabled
- [ ] Table schemas documented
- [ ] Performance requirements defined
- [ ] SSL certificates configured (if needed)
- [ ] Monitoring setup planned
