"""
Typesense Synchronization Service for MySQL-Debezium-Kafka-Typesense Pipeline

This module handles synchronization of data from MySQL (via Kafka) to Typesense search engine.

TYPESENSE ROLE IN PIPELINE:
- Provides fast, typo-tolerant search capabilities
- Indexes data from MySQL for real-time search
- Supports faceted search, filtering, and ranking
- Scales horizontally for high query loads

WHY TYPESENSE SYNC IS NEEDED:
- Transforms MySQL data format to Typesense document format
- Manages Typesense collections (equivalent to database tables)
- Handles schema mapping between MySQL and Typesense
- Provides error handling and retry logic for search operations
"""

import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime
import typesense
from tenacity import retry, stop_after_attempt, wait_exponential

from config import Config

logger = logging.getLogger(__name__)

class TypesenseSync:
    """
    Handles synchronization of data between MySQL and Typesense.

    This class manages:
    - Typesense client connection
    - Collection schema creation and management
    - Document CRUD operations (Create, Read, Update, Delete)
    - Data transformation between MySQL and Typesense formats
    - Error handling and retries
    """

    def __init__(self):
        """
        Initialize Typesense client and set up collections.

        Why initialize collections on startup?
        - Ensures search collections exist before data arrives
        - Validates Typesense connection early
        - Sets up proper schema for optimal search performance
        """
        self.client = self._create_client()
        self._setup_collections()

    @retry(
        stop=stop_after_attempt(Config.RETRY_ATTEMPTS),
        wait=wait_exponential(multiplier=1, min=Config.RETRY_DELAY)
    )
    def _create_client(self) -> typesense.Client:
        """
        Create Typesense client with retry logic.

        Why retry logic?
        - Typesense container might not be ready when this starts
        - Network issues can cause temporary connection failures
        - Improves reliability in distributed environments
        """
        try:
            logger.info("Creating Typesense client...")
            client = typesense.Client(Config.get_typesense_config())

            # Test connection by making a simple API call
            # Try to get collections (this will work even if no collections exist)
            try:
                collections = client.collections.retrieve()
                logger.info(f"Typesense connection successful. Found {len(collections)} collections.")
            except Exception:
                # If collections call fails, try a simple health check via requests
                import requests
                health_url = f"http://{Config.TYPESENSE_HOST}:{Config.TYPESENSE_PORT}/health"
                response = requests.get(health_url)
                if response.status_code == 200:
                    logger.info("Typesense connection successful via health check")
                else:
                    raise Exception(f"Health check failed with status {response.status_code}")

            return client

        except Exception as e:
            logger.error(f"Failed to create Typesense client: {e}")
            raise

    def _get_collection_schema(self, table_name: str) -> Dict[str, Any]:
        """
        Define Typesense collection schema for each MySQL table.

        SCHEMA MAPPING EXPLANATION:
        - MySQL tables → Typesense collections
        - MySQL columns → Typesense fields
        - MySQL data types → Typesense field types

        Why define schemas?
        - Typesense requires explicit field types for optimal search
        - Enables faceted search on specific fields
        - Improves query performance through proper indexing
        - Supports advanced features like geo-search, sorting
        """

        # Base schema common to all collections
        base_schema = {
            'enable_nested_fields': True  # Allow nested JSON objects
            # Note: default_sorting_field removed because int32 fields cannot be used for sorting
        }

        if table_name == 'users':
            return {
                **base_schema,
                'name': 'users',
                'fields': [
                    {'name': 'id', 'type': 'string', 'facet': False},
                    {'name': 'username', 'type': 'string', 'facet': False},
                    {'name': 'email', 'type': 'string', 'facet': False}
                ]
            }
        elif table_name == 'orders':
            return {
                **base_schema,
                'name': 'orders',
                'fields': [
                    {'name': 'id', 'type': 'string', 'facet': False},
                    {'name': 'user_id', 'type': 'int32', 'facet': True},
                    {'name': 'quantity', 'type': 'int32', 'facet': True},
                    {'name': 'total_amount', 'type': 'float', 'facet': True}
                ]
            }
        else:
            # Default schema for unknown tables
            logger.warning(f"No specific schema defined for table: {table_name}")
            return {
                **base_schema,
                'name': table_name,
                'fields': [
                    {'name': 'id', 'type': 'string', 'facet': False}
                ]
            }

    def _setup_collections(self):
        """
        Create Typesense collections for all MySQL tables.

        Why create collections upfront?
        - Ensures collections exist before data arrives from Kafka
        - Validates schema definitions early
        - Avoids race conditions during data ingestion
        """
        # TODO: Update this list with actual production tables
        tables = [
            'users', 'orders', 'products', 'categories',
            'order_items', 'payments', 'addresses',
            'user_preferences', 'product_variants', 'stock_levels'
            # Add more tables as needed from production database
        ]

        for table in tables:
            try:
                schema = self._get_collection_schema(table)

                # Check if collection already exists
                try:
                    existing = self.client.collections[table].retrieve()
                    logger.info(f"Collection '{table}' already exists")
                    continue
                except typesense.exceptions.ObjectNotFound:
                    # Collection doesn't exist, create it
                    pass

                # Create the collection
                self.client.collections.create(schema)
                logger.info(f"Created Typesense collection: {table}")

            except Exception as e:
                logger.error(f"Failed to create collection {table}: {e}")
                # Don't raise here - allow other collections to be created

    def _transform_mysql_to_typesense(self, table_name: str, mysql_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform MySQL row data to Typesense document format.

        TRANSFORMATION RULES:
        - Convert MySQL timestamps to Unix timestamps (int64)
        - Handle NULL values appropriately
        - Ensure data types match Typesense schema
        - Convert boolean values properly

        Why transformation needed?
        - MySQL and Typesense have different data type systems
        - Typesense requires specific formats for optimal search
        - Need to handle edge cases like NULL values
        """
        if not mysql_data:
            return {}

        # Create a copy to avoid modifying original data
        typesense_doc = mysql_data.copy()

        # Convert timestamp fields to Unix timestamps
        timestamp_fields = ['created_at', 'updated_at', 'order_date']
        for field in timestamp_fields:
            if field in typesense_doc and typesense_doc[field]:
                try:
                    # If it's already a timestamp string, convert to Unix timestamp
                    if isinstance(typesense_doc[field], str):
                        from datetime import datetime
                        dt = datetime.fromisoformat(typesense_doc[field].replace('Z', '+00:00'))
                        typesense_doc[field] = int(dt.timestamp())
                    elif isinstance(typesense_doc[field], (int, float)):
                        # Already a timestamp, ensure it's an integer
                        typesense_doc[field] = int(typesense_doc[field])
                except Exception as e:
                    logger.warning(f"Failed to convert timestamp {field}: {e}")
                    # Remove invalid timestamp to avoid schema errors
                    typesense_doc.pop(field, None)

        # Handle boolean fields
        if 'is_active' in typesense_doc:
            # Convert MySQL boolean (0/1) to Python boolean
            typesense_doc['is_active'] = bool(typesense_doc['is_active'])

        # Handle price fields (convert string to float)
        if 'price' in typesense_doc and typesense_doc['price']:
            try:
                typesense_doc['price'] = float(typesense_doc['price'])
            except (ValueError, TypeError):
                typesense_doc.pop('price', None)

        if 'total_amount' in typesense_doc and typesense_doc['total_amount']:
            try:
                typesense_doc['total_amount'] = float(typesense_doc['total_amount'])
            except (ValueError, TypeError):
                typesense_doc.pop('total_amount', None)

        # Ensure ID is present and is a string (Typesense requirement)
        if 'id' in typesense_doc:
            typesense_doc['id'] = str(typesense_doc['id'])

        # Remove None values to avoid schema conflicts
        typesense_doc = {k: v for k, v in typesense_doc.items() if v is not None}

        logger.debug(f"Transformed {table_name} document: {typesense_doc}")
        return typesense_doc

    @retry(
        stop=stop_after_attempt(Config.RETRY_ATTEMPTS),
        wait=wait_exponential(multiplier=1, min=Config.RETRY_DELAY)
    )
    def upsert_document(self, table_name: str, document_data: Dict[str, Any]) -> bool:
        """
        Insert or update a document in Typesense collection.

        UPSERT OPERATION:
        - If document exists (same ID), update it
        - If document doesn't exist, create it
        - This handles both INSERT and UPDATE operations from MySQL

        Why upsert instead of separate insert/update?
        - Simplifies code by handling both cases
        - Avoids errors when document already exists
        - Ensures data consistency between MySQL and Typesense
        """
        try:
            # Transform MySQL data to Typesense format
            typesense_doc = self._transform_mysql_to_typesense(table_name, document_data)

            if not typesense_doc or 'id' not in typesense_doc:
                logger.error(f"Invalid document data for {table_name}: missing ID")
                return False

            # Perform upsert operation
            result = self.client.collections[table_name].documents.upsert(typesense_doc)

            logger.debug(f"Upserted document in {table_name}: {result}")
            return True

        except typesense.exceptions.ObjectNotFound:
            logger.error(f"Collection {table_name} not found")
            return False
        except Exception as e:
            logger.error(f"Failed to upsert document in {table_name}: {e}")
            raise  # Re-raise for retry logic

    @retry(
        stop=stop_after_attempt(Config.RETRY_ATTEMPTS),
        wait=wait_exponential(multiplier=1, min=Config.RETRY_DELAY)
    )
    def delete_document(self, table_name: str, document_id: str) -> bool:
        """
        Delete a document from Typesense collection.

        DELETE OPERATION:
        - Removes document with specified ID from collection
        - Handles MySQL DELETE operations
        - Gracefully handles cases where document doesn't exist

        Why separate delete method?
        - DELETE operations require only document ID
        - Different error handling than upsert operations
        - Allows specific logging for delete operations
        """
        try:
            # Ensure document_id is a string
            doc_id = str(document_id)

            # Delete the document
            result = self.client.collections[table_name].documents[doc_id].delete()

            logger.debug(f"Deleted document from {table_name}: {result}")
            return True

        except typesense.exceptions.ObjectNotFound:
            # Document or collection doesn't exist - this is OK for delete
            logger.warning(f"Document {document_id} not found in {table_name} (already deleted?)")
            return True  # Consider this successful
        except Exception as e:
            logger.error(f"Failed to delete document {document_id} from {table_name}: {e}")
            raise  # Re-raise for retry logic