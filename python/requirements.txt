# Python Dependencies for MySQL-Debezium-Kafka-Typesense Pipeline
# Each dependency serves a specific purpose in our data pipeline

# KAFKA CLIENT
# kafka-python: Pure Python Kafka client for consuming messages from Kafka
# Why needed: Receives change events from De<PERSON><PERSON><PERSON> via Kafka topics
kafka-python==2.0.2

# TYPESENSE CLIENT
# typesense: Official Python client for Typesense search engine
# Why needed: Syncs processed data to Typesense for search functionality
typesense==0.15.0

# HTTP CLIENT
# requests: HTTP library for making API calls
# Why needed: Used by Typesense client and for health checks
requests==2.31.0

# JSON PROCESSING
# json: Built-in Python module (no installation needed)
# Why needed: Parse Debezium change events which are in JSON format

# MYSQL CLIENT (for direct database queries if needed)
# PyMySQL: Pure Python MySQL client
# Why needed: Direct database access for validation and troubleshooting
PyMySQL==1.1.0

# LOGGING AND UTILITIES
# python-dotenv: Load environment variables from .env file
# Why needed: Configuration management for different environments
python-dotenv==1.0.0

# ASYNC SUPPORT (if needed for high throughput)
# asyncio: Built-in Python module for asynchronous programming
# Why needed: Handle multiple Kafka partitions concurrently

# DATE/TIME HANDLING
# python-dateutil: Extensions to the standard datetime module
# Why needed: Parse timestamps from Debezium change events
python-dateutil==2.8.2

# RETRY LOGIC
# tenacity: Retry library for Python
# Why needed: Handle transient failures in Kafka/Typesense connections
tenacity==8.2.3