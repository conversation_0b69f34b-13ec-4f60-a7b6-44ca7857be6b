# Dockerfile for Python Kafka Consumer Service
# This creates a containerized environment for our Python application

# Use official Python runtime as base image
# Why Python 3.11? - Latest stable version with good performance
FROM python:3.11-slim

# Set working directory inside container
# Why /app? - Standard convention for application directory
WORKDIR /app

# Install system dependencies
# Why these packages?
# - gcc: Required for compiling some Python packages
# - default-libmysqlclient-dev: MySQL client libraries for PyMySQL
# - pkg-config: Helps find installed libraries
RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file first
# Why copy requirements first?
# - Docker layer caching: if requirements don't change, this layer is reused
# - Faster builds when only source code changes
COPY requirements.txt .

# Install Python dependencies
# Why --no-cache-dir? - Reduces image size by not storing pip cache
# Why --upgrade pip? - Ensures latest pip version for better dependency resolution
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy application source code
# Why copy after installing dependencies?
# - Source code changes more frequently than dependencies
# - Better Docker layer caching and faster rebuilds
COPY . .

# Create non-root user for security
# Why non-root user?
# - Security best practice: don't run containers as root
# - Reduces attack surface if container is compromised
RUN useradd --create-home --shell /bin/bash app && \
    chown -R app:app /app
USER app

# Set environment variables
# Why set these?
# - PYTHONUNBUFFERED: Ensures Python output is not buffered (better for logs)
# - PYTHONPATH: Ensures Python can find our modules
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app

# Health check to verify container is working
# Why health check?
# - Docker can restart unhealthy containers
# - Load balancers can route traffic away from unhealthy instances
# - Monitoring systems can detect issues
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import kafka; print('Health check passed')" || exit 1

# Default command to run the application
# Why use CMD instead of RUN?
# - CMD runs when container starts, RUN runs during build
# - Can be overridden when running container
CMD ["python", "kafka_consumer.py"]