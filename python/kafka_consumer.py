"""
Kafka Consumer for MySQL-Debezium-Kafka-Typesense Pipeline

This module consumes change events from Kafka topics created by <PERSON><PERSON><PERSON><PERSON>
and processes them for synchronization with Typesense.

WORKFLOW EXPLANATION:
1. <PERSON><PERSON><PERSON><PERSON> captures MySQL changes → Kafka topics
2. This consumer reads from Kafka topics
3. Processes change events (INSERT, UPDATE, DELETE)
4. Sends processed data to Typesense via typesense_sync module

WHY KAFKA CONSUMER IS NEEDED:
- Decouples database changes from search index updates
- Provides fault tolerance and replay capability
- Enables horizontal scaling of processing
- Handles backpressure when Typesense is slow
"""

import json
import logging
import signal
import sys
import time
from typing import Dict, Any, Optional
from kafka import KafkaConsumer
from kafka.errors import KafkaError
from tenacity import retry, stop_after_attempt, wait_exponential

from config import Config
from typesense_sync import TypesenseSync

# Configure logging
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DebeziumKafkaConsumer:
    """
    Main Kafka consumer class that processes Debezium change events.

    This class handles:
    - Connecting to Kafka cluster
    - Consuming messages from Debezium topics
    - Parsing Debezium change event format
    - Routing events to appropriate handlers
    - Error handling and retries
    """

    def __init__(self):
        """
        Initialize the Kafka consumer and Typesense sync handler.

        Why separate initialization?
        - Allows validation before starting consumption
        - Makes testing easier by mocking dependencies
        """
        self.consumer: Optional[KafkaConsumer] = None
        self.typesense_sync = TypesenseSync()
        self.running = False

        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """
        Handle shutdown signals gracefully.

        Why needed?
        - Ensures consumer commits offsets before shutdown
        - Prevents data loss during container restarts
        - Allows clean resource cleanup
        """
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.running = False

    @retry(
        stop=stop_after_attempt(Config.RETRY_ATTEMPTS),
        wait=wait_exponential(multiplier=1, min=Config.RETRY_DELAY)
    )
    def _create_consumer(self) -> KafkaConsumer:
        """
        Create and configure Kafka consumer with retry logic.

        Why retry logic?
        - Kafka might not be ready when container starts
        - Network issues can cause temporary connection failures
        - Improves reliability in distributed environments
        """
        try:
            logger.info("Creating Kafka consumer...")
            consumer = KafkaConsumer(
                *Config.KAFKA_TOPICS,
                **Config.get_kafka_config()
            )
            logger.info(f"Successfully connected to Kafka, subscribed to topics: {Config.KAFKA_TOPICS}")
            return consumer

        except KafkaError as e:
            logger.error(f"Failed to create Kafka consumer: {e}")
            raise

    def parse_debezium_event(self, message_value: str) -> Optional[Dict[str, Any]]:
        """
        Parse Debezium change event message.

        DEBEZIUM MESSAGE FORMAT:
        {
            "before": {...},     # Row data before change (null for INSERT)
            "after": {...},      # Row data after change (null for DELETE)
            "source": {...},     # Metadata about the change
            "op": "c|u|d|r",    # Operation: create, update, delete, read
            "ts_ms": 1234567890  # Timestamp in milliseconds
        }

        Why parse this format?
        - Debezium provides rich metadata about changes
        - Need to extract actual data from "before"/"after" fields
        - Operation type determines how to update Typesense
        """
        try:
            event = json.loads(message_value)

            # Extract key information from Debezium event
            operation = event.get('op')
            before_data = event.get('before')
            after_data = event.get('after')
            source_info = event.get('source', {})
            timestamp = event.get('ts_ms')

            # Log the event for debugging
            logger.debug(f"Parsed event - Operation: {operation}, Table: {source_info.get('table')}")

            return {
                'operation': operation,
                'before': before_data,
                'after': after_data,
                'table': source_info.get('table'),
                'database': source_info.get('db'),
                'timestamp': timestamp
            }

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON message: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error parsing message: {e}")
            return None

    def process_change_event(self, parsed_event: Dict[str, Any], topic: str) -> bool:
        """
        Process a single change event and sync to Typesense.

        OPERATION TYPES:
        - 'c' (CREATE/INSERT): New record added
        - 'u' (UPDATE): Existing record modified
        - 'd' (DELETE): Record removed
        - 'r' (READ): Initial snapshot read

        Why different handling for each operation?
        - INSERT/READ: Add new document to Typesense
        - UPDATE: Update existing document in Typesense
        - DELETE: Remove document from Typesense
        """
        operation = parsed_event['operation']
        table = parsed_event['table']

        try:
            if operation in ['c', 'r']:  # CREATE or READ (initial snapshot)
                # For INSERT operations, use 'after' data (new record)
                document_data = parsed_event['after']
                if document_data:
                    success = self.typesense_sync.upsert_document(table, document_data)
                    if success:
                        logger.info(f"Successfully inserted document to {table}: ID {document_data.get('id')}")
                    return success

            elif operation == 'u':  # UPDATE
                # For UPDATE operations, use 'after' data (updated record)
                document_data = parsed_event['after']
                if document_data:
                    success = self.typesense_sync.upsert_document(table, document_data)
                    if success:
                        logger.info(f"Successfully updated document in {table}: ID {document_data.get('id')}")
                    return success

            elif operation == 'd':  # DELETE
                # For DELETE operations, use 'before' data to get ID
                before_data = parsed_event['before']
                if before_data and 'id' in before_data:
                    document_id = str(before_data['id'])
                    success = self.typesense_sync.delete_document(table, document_id)
                    if success:
                        logger.info(f"Successfully deleted document from {table}: ID {document_id}")
                    return success

            else:
                logger.warning(f"Unknown operation type: {operation}")
                return False

        except Exception as e:
            logger.error(f"Error processing {operation} event for table {table}: {e}")
            return False

        return False

    def start_consuming(self):
        """
        Main consumption loop that processes messages from Kafka.

        CONSUMPTION FLOW:
        1. Create Kafka consumer connection
        2. Poll for messages from subscribed topics
        3. Parse each message as Debezium event
        4. Process event and sync to Typesense
        5. Handle errors and continue processing

        Why continuous loop?
        - Kafka is a streaming platform, messages arrive continuously
        - Need to process messages as they arrive for real-time sync
        - Loop allows handling of connection failures and retries
        """
        if not Config.validate_config():
            logger.error("Configuration validation failed")
            sys.exit(1)

        try:
            # Create Kafka consumer with retry logic
            self.consumer = self._create_consumer()
            self.running = True

            logger.info("Starting message consumption...")

            # Main consumption loop
            while self.running:
                try:
                    # Poll for messages (timeout prevents blocking forever)
                    message_batch = self.consumer.poll(timeout_ms=1000)

                    if not message_batch:
                        continue  # No messages, continue polling

                    # Process each message in the batch
                    for topic_partition, messages in message_batch.items():
                        topic_name = topic_partition.topic

                        for message in messages:
                            try:
                                # Parse Debezium event
                                parsed_event = self.parse_debezium_event(message.value)

                                if parsed_event:
                                    # Process the change event
                                    success = self.process_change_event(parsed_event, topic_name)

                                    if not success:
                                        logger.warning(f"Failed to process message from {topic_name}")
                                else:
                                    logger.warning(f"Failed to parse message from {topic_name}")

                            except Exception as e:
                                logger.error(f"Error processing message from {topic_name}: {e}")
                                continue

                    # Commit offsets after processing batch
                    self.consumer.commit()

                except KafkaError as e:
                    logger.error(f"Kafka error: {e}")
                    time.sleep(Config.RETRY_DELAY)
                    continue

                except Exception as e:
                    logger.error(f"Unexpected error in consumption loop: {e}")
                    time.sleep(Config.RETRY_DELAY)
                    continue

        except Exception as e:
            logger.error(f"Fatal error in consumer: {e}")
            sys.exit(1)

        finally:
            self._cleanup()

    def _cleanup(self):
        """
        Clean up resources before shutdown.

        Why cleanup needed?
        - Close Kafka consumer connection properly
        - Commit any pending offsets
        - Prevent resource leaks
        """
        logger.info("Cleaning up resources...")

        if self.consumer:
            try:
                self.consumer.close()
                logger.info("Kafka consumer closed successfully")
            except Exception as e:
                logger.error(f"Error closing Kafka consumer: {e}")

def main():
    """
    Main entry point for the Kafka consumer application.

    Why separate main function?
    - Makes the module importable for testing
    - Provides clear entry point for container
    - Allows proper error handling at top level
    """
    logger.info("Starting Debezium Kafka Consumer...")

    consumer = DebeziumKafkaConsumer()
    consumer.start_consuming()

if __name__ == "__main__":
    main()