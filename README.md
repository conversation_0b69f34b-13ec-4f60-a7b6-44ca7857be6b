# MySQL-Debezium-Kafka-Typesense Data Pipeline

## Overview
This project demonstrates a complete real-time data pipeline that captures changes from MySQL database and syncs them to Typesense search engine using Debezium and Kafka.

## Architecture Flow
```
MySQL Database → Debezium → Kafka → Python Consumer → Typesense
```

## Project Structure
```
.
├── docker-compose.yml          # Main Docker orchestration file
├── mysql/
│   ├── init.sql               # Database initialization script
│   └── my.cnf                 # MySQL configuration for CDC
├── debezium/
│   └── mysql-connector.json   # Debezium connector configuration
├── python/
│   ├── requirements.txt       # Python dependencies
│   ├── kafka_consumer.py      # Main Kafka consumer application
│   ├── typesense_sync.py      # Typesense synchronization service
│   └── config.py              # Configuration settings
├── scripts/
│   ├── setup.sh              # Setup and initialization script
│   ├── test_pipeline.py      # Pipeline testing script
│   └── cleanup.sh            # Cleanup script
└── README.md                 # This file
```

## Quick Start
1. `chmod +x scripts/setup.sh && ./scripts/setup.sh`
2. `docker-compose up -d`
3. Wait for all services to start (check with `docker-compose ps`)
4. Run tests: `python scripts/test_pipeline.py`

## Detailed Workflow Explanation

### 🔄 Complete Data Flow
```
MySQL Database Changes → Debezium CDC → Kafka Topics → Python Consumer → Typesense Search
```

### 📊 Component Roles and Interactions

#### 1. MySQL Database (Source of Truth)
- **Role**: Primary data storage for application
- **Configuration**: Enabled with binary logging for Change Data Capture
- **Key Settings**:
  - `log-bin=mysql-bin`: Enables binary logging
  - `binlog-format=ROW`: Logs actual row changes (required for Debezium)
  - `server-id=1`: Unique server identifier for replication

#### 2. Debezium (Change Data Capture Engine)
- **Role**: Monitors MySQL binary logs and captures changes
- **How it works**:
  1. Connects to MySQL as a replication slave
  2. Reads binary log entries in real-time
  3. Converts log entries to structured change events
  4. Publishes events to Kafka topics
- **Event Format**: JSON with `before`, `after`, `op` (operation), and metadata

#### 3. Kafka (Message Broker)
- **Role**: Reliable message streaming between Debezium and consumers
- **Topics Created**: `users`, `products`, `orders` (one per table)
- **Benefits**:
  - Decouples data capture from processing
  - Provides fault tolerance and replay capability
  - Enables multiple consumers for same data

#### 4. Python Consumer (Data Processor)
- **Role**: Consumes Kafka messages and transforms data for Typesense
- **Processing Steps**:
  1. Polls Kafka topics for new messages
  2. Parses Debezium change events
  3. Transforms MySQL data to Typesense format
  4. Performs CRUD operations on Typesense collections
- **Error Handling**: Retries failed operations with exponential backoff

#### 5. Typesense (Search Engine)
- **Role**: Provides fast, typo-tolerant search capabilities
- **Collections**: Mirror MySQL tables with optimized schemas
- **Features**: Full-text search, faceted search, sorting, filtering

### 🚀 Setup Instructions

#### Prerequisites
- Docker and Docker Compose installed
- At least 4GB RAM available
- Ports 3306, 8083, 8108, 9092, 29092, 2181 available

#### Step 1: Clone and Setup
```bash
# Make setup script executable
chmod +x scripts/setup.sh

# Run automated setup
./scripts/setup.sh
```

#### Step 2: Verify Services
```bash
# Check all services are running
docker-compose ps

# Expected output: All services should be "Up"
```

#### Step 3: Test the Pipeline
```bash
# Install Python dependencies for testing
pip install pymysql kafka-python requests

# Run comprehensive tests
python scripts/test_pipeline.py
```

### 🧪 Testing Commands

#### Manual Database Operations
```bash
# Connect to MySQL
docker exec -it mysql mysql -u dbuser -p testdb

# Insert test record
INSERT INTO users (username, email, full_name, age, city)
VALUES ('test_user', '<EMAIL>', 'Test User', 25, 'Test City');

# Update record
UPDATE users SET city = 'Updated City' WHERE username = 'test_user';

# Delete record
DELETE FROM users WHERE username = 'test_user';
```

#### Kafka Topic Monitoring
```bash
# List all topics
docker exec kafka kafka-topics --bootstrap-server localhost:9092 --list

# Monitor messages in users topic
docker exec kafka kafka-console-consumer --bootstrap-server localhost:9092 --topic users --from-beginning
```

#### Typesense Search Testing
```bash
# Search for users
curl "http://localhost:8108/collections/users/documents/search?q=john&query_by=full_name" \
  -H "X-TYPESENSE-API-KEY: xyz123"

# Search products by category
curl "http://localhost:8108/collections/products/documents/search?q=*&query_by=name&filter_by=category:Electronics" \
  -H "X-TYPESENSE-API-KEY: xyz123"

# Get collection info
curl "http://localhost:8108/collections/users" \
  -H "X-TYPESENSE-API-KEY: xyz123"
```

#### Debezium Connector Management
```bash
# Check connector status
curl http://localhost:8083/connectors/mysql-connector/status

# List all connectors
curl http://localhost:8083/connectors

# Restart connector if needed
curl -X POST http://localhost:8083/connectors/mysql-connector/restart
```

### 🔧 Troubleshooting Guide

#### Common Issues and Solutions

**1. Services Not Starting**
```bash
# Check Docker resources
docker system df
docker system prune  # If low on space

# Check service logs
docker-compose logs mysql
docker-compose logs kafka
docker-compose logs debezium
```

**2. Debezium Connector Fails**
```bash
# Check MySQL binary logging
docker exec mysql mysql -u root -p -e "SHOW MASTER STATUS;"

# Verify user permissions
docker exec mysql mysql -u root -p -e "SHOW GRANTS FOR 'dbuser'@'%';"

# Reset connector
curl -X DELETE http://localhost:8083/connectors/mysql-connector
curl -X POST http://localhost:8083/connectors -H "Content-Type: application/json" -d @debezium/mysql-connector.json
```

**3. Python Consumer Issues**
```bash
# Check consumer logs
docker-compose logs python-consumer

# Restart consumer
docker-compose restart python-consumer

# Check Kafka connectivity
docker exec python-consumer python -c "from kafka import KafkaConsumer; print('Kafka OK')"
```

**4. Typesense Connection Problems**
```bash
# Check Typesense health
curl http://localhost:8108/health

# Verify API key
curl http://localhost:8108/collections -H "X-TYPESENSE-API-KEY: xyz123"

# Reset collections
curl -X DELETE http://localhost:8108/collections/users -H "X-TYPESENSE-API-KEY: xyz123"
```

### 📈 Performance Considerations

#### Scaling the Pipeline

**1. Kafka Partitioning**
- Increase partitions for higher throughput
- Multiple consumer instances can process different partitions

**2. Typesense Clustering**
- Add multiple Typesense nodes for high availability
- Configure replication for data redundancy

**3. Python Consumer Optimization**
- Batch processing for better performance
- Async processing for concurrent operations
- Multiple consumer instances for load distribution

#### Monitoring and Metrics

**1. Service Health Checks**
```bash
# MySQL
docker exec mysql mysqladmin ping -u root -p

# Kafka
docker exec kafka kafka-broker-api-versions --bootstrap-server localhost:9092

# Typesense
curl http://localhost:8108/health

# Debezium
curl http://localhost:8083/connectors/mysql-connector/status
```

**2. Performance Metrics**
```bash
# Kafka consumer lag
docker exec kafka kafka-consumer-groups --bootstrap-server localhost:9092 --describe --group typesense-sync-group

# Typesense collection stats
curl http://localhost:8108/collections/users -H "X-TYPESENSE-API-KEY: xyz123"
```

### 🔒 Security Considerations

#### Production Deployment

**1. Authentication & Authorization**
- Change default passwords and API keys
- Use proper MySQL user permissions
- Enable Kafka SASL/SSL authentication
- Configure Typesense API key rotation

**2. Network Security**
- Use Docker networks for service isolation
- Configure firewall rules for external access
- Enable SSL/TLS for all communications

**3. Data Protection**
- Encrypt sensitive data in MySQL
- Use Kafka encryption at rest and in transit
- Regular backups of MySQL and Typesense data

### 🧹 Cleanup

```bash
# Stop and remove all services
./scripts/cleanup.sh

# Force cleanup without prompts
./scripts/cleanup.sh --force
```

### 📚 Additional Resources

#### Understanding Each Component

**MySQL Binary Logging**
- [MySQL Binary Log Documentation](https://dev.mysql.com/doc/refman/8.0/en/binary-log.html)
- Why needed: Debezium reads from binary logs to capture changes

**Debezium MySQL Connector**
- [Debezium MySQL Connector Docs](https://debezium.io/documentation/reference/connectors/mysql.html)
- How it works: Acts as MySQL replication slave

**Kafka Fundamentals**
- [Apache Kafka Documentation](https://kafka.apache.org/documentation/)
- Role: Reliable message streaming and buffering

**Typesense Search**
- [Typesense Documentation](https://typesense.org/docs/)
- Benefits: Fast, typo-tolerant search with faceting

#### Code Structure Explanation

**Configuration Management (`config.py`)**
- Centralizes all settings
- Environment variable based configuration
- Validation and error handling

**Kafka Consumer (`kafka_consumer.py`)**
- Handles Debezium message parsing
- Implements retry logic and error handling
- Manages consumer lifecycle and graceful shutdown

**Typesense Sync (`typesense_sync.py`)**
- Transforms MySQL data to Typesense format
- Manages collection schemas and CRUD operations
- Handles data type conversions and validation

### 🎯 Next Steps

1. **Customize for Your Use Case**
   - Modify MySQL schema for your data model
   - Update Typesense schemas for your search requirements
   - Adjust Python consumer logic for your business rules

2. **Add Monitoring**
   - Implement logging and metrics collection
   - Set up alerts for service failures
   - Monitor data freshness and sync delays

3. **Scale for Production**
   - Configure multiple Kafka partitions
   - Deploy multiple consumer instances
   - Set up Typesense clustering

4. **Enhance Security**
   - Change all default credentials
   - Enable SSL/TLS encryption
   - Implement proper access controls

---

## 🎉 SETUP COMPLETE!

### ✅ What's Working:
- **MySQL Database**: Running with CDC enabled
- **Debezium Connector**: Capturing all database changes
- **Kafka Topics**: Streaming change events (`users`, `products`, `orders`)
- **Python Consumer**: Processing messages and syncing to Typesense
- **Typesense Search**: Full-text search with real-time data sync

### 🧪 Tested Operations:
- ✅ **INSERT**: New records automatically appear in search
- ✅ **UPDATE**: Changes sync to search engine in real-time
- ✅ **DELETE**: Records removed from search index
- ✅ **SEARCH**: Full-text search with highlighting and facets

### 🚀 Quick Commands to Test:

```bash
# 1. Check all services are running
docker-compose ps

# 2. Add a new user
docker exec mysql mysql -u dbuser -p'dbpassword' testdb -e "INSERT INTO users (username, email, full_name, age, city) VALUES ('testuser', '<EMAIL>', 'Test User', 25, 'New York');"

# 3. Search for the user
curl "http://localhost:8108/collections/users/documents/search?q=testuser&query_by=username" -H "X-TYPESENSE-API-KEY: xyz123"

# 4. Update the user
docker exec mysql mysql -u dbuser -p'dbpassword' testdb -e "UPDATE users SET city = 'San Francisco' WHERE username = 'testuser';"

# 5. Search again to see the update
curl "http://localhost:8108/collections/users/documents/search?q=testuser&query_by=username" -H "X-TYPESENSE-API-KEY: xyz123"

# 6. Add a product
docker exec mysql mysql -u dbuser -p'dbpassword' testdb -e "INSERT INTO products (name, description, price, category, stock_quantity) VALUES ('iPhone 15', 'Latest iPhone model', 999.99, 'Electronics', 50);"

# 7. Search products
curl "http://localhost:8108/collections/products/documents/search?q=iPhone&query_by=name" -H "X-TYPESENSE-API-KEY: xyz123"

# 8. Filter by category
curl "http://localhost:8108/collections/products/documents/search?q=*&query_by=name&filter_by=category:Electronics" -H "X-TYPESENSE-API-KEY: xyz123"
```

### 📊 Service URLs:
- **MySQL**: `localhost:3306` (user: `dbuser`, password: `dbpassword`, database: `testdb`)
- **Kafka**: `localhost:29092`
- **Debezium Connect**: `http://localhost:8083`
- **Typesense**: `http://localhost:8108` (API Key: `xyz123`)

### 🔧 Management Commands:
```bash
# View logs
docker-compose logs -f python-consumer
docker-compose logs -f debezium

# Restart services
docker-compose restart python-consumer
docker-compose restart debezium

# Stop everything
docker-compose down

# Clean up completely
./scripts/cleanup.sh
```

**🎯 The complete MySQL → Debezium → Kafka → Python → Typesense pipeline is now fully operational!**
```