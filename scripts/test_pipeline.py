#!/usr/bin/env python3
"""
Comprehensive Test Script for MySQL-Debezium-Kafka-Typesense Pipeline

This script tests the entire data pipeline by:
1. Connecting to MySQL and performing CRUD operations
2. Verifying Kafka topics receive change events
3. Checking Typesense collections are updated
4. Testing search functionality

WHY COMPREHENSIVE TESTING IS NEEDED:
- Validates entire pipeline end-to-end
- Catches integration issues between services
- Provides confidence that the system works correctly
- Helps troubleshoot problems by isolating failures
"""

import json
import time
import sys
import requests
import pymysql
from kafka import KafkaConsumer
from typing import Dict, Any, List, Optional

class PipelineTester:
    """
    Comprehensive tester for the entire data pipeline.

    This class tests each component and their integration:
    - MySQL database operations
    - Kafka message consumption
    - Typesense search functionality
    - End-to-end data flow
    """

    def __init__(self):
        """Initialize connections to all services."""
        self.mysql_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'dbuser',
            'password': 'dbpassword',
            'database': 'testdb'
        }

        self.typesense_config = {
            'host': 'localhost',
            'port': 8108,
            'api_key': 'xyz123'
        }

        self.kafka_config = {
            'bootstrap_servers': ['localhost:29092'],
            'group_id': 'test-consumer-group',
            'auto_offset_reset': 'latest',
            'consumer_timeout_ms': 5000
        }

    def test_mysql_connection(self) -> bool:
        """
        Test MySQL database connection and basic operations.

        Why test MySQL first?
        - It's the source of truth for our data
        - If MySQL fails, the entire pipeline fails
        - Validates database schema and permissions
        """
        print("🔍 Testing MySQL connection...")

        try:
            connection = pymysql.connect(**self.mysql_config)
            cursor = connection.cursor()

            # Test basic query
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            print(f"   ✅ MySQL connected successfully. Users count: {user_count}")

            # Test table structure
            cursor.execute("SHOW TABLES")
            tables = [row[0] for row in cursor.fetchall()]
            expected_tables = ['users', 'products', 'orders']

            for table in expected_tables:
                if table in tables:
                    print(f"   ✅ Table '{table}' exists")
                else:
                    print(f"   ❌ Table '{table}' missing")
                    return False

            cursor.close()
            connection.close()
            return True

        except Exception as e:
            print(f"   ❌ MySQL connection failed: {e}")
            return False

    def test_kafka_topics(self) -> bool:
        """
        Test Kafka topics and message consumption.

        Why test Kafka topics?
        - Validates Debezium is creating topics correctly
        - Ensures Kafka is accessible from Python
        - Checks message format and content
        """
        print("🔍 Testing Kafka topics...")

        try:
            # Create consumer to check topics
            consumer = KafkaConsumer(**self.kafka_config)

            # Get available topics
            topics = consumer.topics()
            expected_topics = ['users', 'products', 'orders']

            for topic in expected_topics:
                if topic in topics:
                    print(f"   ✅ Kafka topic '{topic}' exists")
                else:
                    print(f"   ❌ Kafka topic '{topic}' missing")
                    consumer.close()
                    return False

            consumer.close()
            return True

        except Exception as e:
            print(f"   ❌ Kafka connection failed: {e}")
            return False

    def test_typesense_connection(self) -> bool:
        """
        Test Typesense connection and collections.

        Why test Typesense?
        - Validates search engine is accessible
        - Checks collections are created correctly
        - Ensures API authentication works
        """
        print("🔍 Testing Typesense connection...")

        try:
            # Test health endpoint
            health_url = f"http://{self.typesense_config['host']}:{self.typesense_config['port']}/health"
            response = requests.get(health_url)

            if response.status_code == 200:
                print("   ✅ Typesense health check passed")
            else:
                print(f"   ❌ Typesense health check failed: {response.status_code}")
                return False

            # Test collections endpoint
            collections_url = f"http://{self.typesense_config['host']}:{self.typesense_config['port']}/collections"
            headers = {'X-TYPESENSE-API-KEY': self.typesense_config['api_key']}
            response = requests.get(collections_url, headers=headers)

            if response.status_code == 200:
                collections = response.json()
                collection_names = [col['name'] for col in collections]
                expected_collections = ['users', 'products', 'orders']

                for collection in expected_collections:
                    if collection in collection_names:
                        print(f"   ✅ Typesense collection '{collection}' exists")
                    else:
                        print(f"   ⚠️  Typesense collection '{collection}' missing (will be created)")

                return True
            else:
                print(f"   ❌ Failed to get Typesense collections: {response.status_code}")
                return False

        except Exception as e:
            print(f"   ❌ Typesense connection failed: {e}")
            return False

    def test_end_to_end_pipeline(self) -> bool:
        """
        Test the complete pipeline by making database changes and verifying sync.

        END-TO-END TEST FLOW:
        1. Insert new record in MySQL
        2. Wait for Debezium to capture change
        3. Verify Kafka receives message
        4. Wait for Python consumer to process
        5. Verify Typesense is updated
        6. Test search functionality

        Why end-to-end testing?
        - Validates complete data flow
        - Catches timing and synchronization issues
        - Ensures real-world usage scenarios work
        """
        print("🔍 Testing end-to-end pipeline...")

        try:
            # Step 1: Insert test record in MySQL
            connection = pymysql.connect(**self.mysql_config)
            cursor = connection.cursor()

            test_user = {
                'username': 'test_pipeline_user',
                'email': '<EMAIL>',
                'full_name': 'Pipeline Test User',
                'age': 30,
                'city': 'Test City'
            }

            # Insert test user
            insert_query = """
                INSERT INTO users (username, email, full_name, age, city)
                VALUES (%(username)s, %(email)s, %(full_name)s, %(age)s, %(city)s)
            """
            cursor.execute(insert_query, test_user)
            test_user_id = cursor.lastrowid
            connection.commit()

            print(f"   ✅ Inserted test user with ID: {test_user_id}")

            # Step 2: Wait for pipeline to process (give it time to sync)
            print("   ⏳ Waiting for pipeline to process changes...")
            time.sleep(10)  # Wait for Debezium → Kafka → Python Consumer → Typesense

            # Step 3: Verify record exists in Typesense
            search_url = f"http://{self.typesense_config['host']}:{self.typesense_config['port']}/collections/users/documents/search"
            headers = {'X-TYPESENSE-API-KEY': self.typesense_config['api_key']}
            params = {
                'q': test_user['username'],
                'query_by': 'username'
            }

            response = requests.get(search_url, headers=headers, params=params)

            if response.status_code == 200:
                search_results = response.json()
                if search_results['found'] > 0:
                    found_user = search_results['hits'][0]['document']
                    print(f"   ✅ Found user in Typesense: {found_user['username']}")

                    # Verify data integrity
                    if (found_user['email'] == test_user['email'] and
                        found_user['full_name'] == test_user['full_name']):
                        print("   ✅ Data integrity verified")
                    else:
                        print("   ❌ Data integrity check failed")
                        return False
                else:
                    print("   ❌ Test user not found in Typesense")
                    return False
            else:
                print(f"   ❌ Typesense search failed: {response.status_code}")
                return False

            # Step 4: Test UPDATE operation
            print("   🔄 Testing UPDATE operation...")
            update_query = "UPDATE users SET city = %s WHERE id = %s"
            cursor.execute(update_query, ('Updated City', test_user_id))
            connection.commit()

            # Wait for update to sync
            time.sleep(5)

            # Verify update in Typesense
            response = requests.get(search_url, headers=headers, params=params)
            if response.status_code == 200:
                search_results = response.json()
                if search_results['found'] > 0:
                    updated_user = search_results['hits'][0]['document']
                    if updated_user['city'] == 'Updated City':
                        print("   ✅ UPDATE operation synced successfully")
                    else:
                        print(f"   ❌ UPDATE not synced. Expected 'Updated City', got '{updated_user['city']}'")
                        return False

            # Step 5: Test DELETE operation
            print("   🗑️  Testing DELETE operation...")
            delete_query = "DELETE FROM users WHERE id = %s"
            cursor.execute(delete_query, (test_user_id,))
            connection.commit()

            # Wait for delete to sync
            time.sleep(5)

            # Verify deletion in Typesense
            response = requests.get(search_url, headers=headers, params=params)
            if response.status_code == 200:
                search_results = response.json()
                if search_results['found'] == 0:
                    print("   ✅ DELETE operation synced successfully")
                else:
                    print("   ❌ DELETE not synced - record still exists in Typesense")
                    return False

            cursor.close()
            connection.close()
            return True

        except Exception as e:
            print(f"   ❌ End-to-end test failed: {e}")
            return False

    def test_search_functionality(self) -> bool:
        """
        Test Typesense search capabilities with existing data.

        SEARCH TESTS:
        - Basic text search
        - Faceted search (filtering)
        - Sorting
        - Pagination

        Why test search functionality?
        - Validates the primary purpose of Typesense
        - Ensures search performance is acceptable
        - Tests different query types applications might use
        """
        print("🔍 Testing search functionality...")

        try:
            headers = {'X-TYPESENSE-API-KEY': self.typesense_config['api_key']}
            base_url = f"http://{self.typesense_config['host']}:{self.typesense_config['port']}"

            # Test 1: Basic text search on products
            search_url = f"{base_url}/collections/products/documents/search"
            params = {
                'q': 'laptop',
                'query_by': 'name,description'
            }

            response = requests.get(search_url, headers=headers, params=params)
            if response.status_code == 200:
                results = response.json()
                print(f"   ✅ Text search found {results['found']} products")
            else:
                print(f"   ❌ Text search failed: {response.status_code}")
                return False

            # Test 2: Faceted search (filter by category)
            params = {
                'q': '*',
                'query_by': 'name',
                'filter_by': 'category:Electronics'
            }

            response = requests.get(search_url, headers=headers, params=params)
            if response.status_code == 200:
                results = response.json()
                print(f"   ✅ Faceted search found {results['found']} electronics")
            else:
                print(f"   ❌ Faceted search failed: {response.status_code}")
                return False

            # Test 3: Sorting by price
            params = {
                'q': '*',
                'query_by': 'name',
                'sort_by': 'price:desc'
            }

            response = requests.get(search_url, headers=headers, params=params)
            if response.status_code == 200:
                results = response.json()
                print(f"   ✅ Sorted search returned {results['found']} products")
            else:
                print(f"   ❌ Sorted search failed: {response.status_code}")
                return False

            return True

        except Exception as e:
            print(f"   ❌ Search functionality test failed: {e}")
            return False

    def run_all_tests(self) -> bool:
        """
        Run all tests in sequence and report results.

        TEST SEQUENCE:
        1. Basic connectivity tests
        2. End-to-end pipeline test
        3. Search functionality test

        Why run in this order?
        - Basic tests validate prerequisites
        - End-to-end test validates core functionality
        - Search test validates final output
        """
        print("🧪 Starting comprehensive pipeline tests...\n")

        tests = [
            ("MySQL Connection", self.test_mysql_connection),
            ("Kafka Topics", self.test_kafka_topics),
            ("Typesense Connection", self.test_typesense_connection),
            ("End-to-End Pipeline", self.test_end_to_end_pipeline),
            ("Search Functionality", self.test_search_functionality)
        ]

        passed_tests = 0
        total_tests = len(tests)

        for test_name, test_function in tests:
            print(f"\n{'='*50}")
            print(f"Running: {test_name}")
            print('='*50)

            try:
                if test_function():
                    print(f"✅ {test_name}: PASSED")
                    passed_tests += 1
                else:
                    print(f"❌ {test_name}: FAILED")
            except Exception as e:
                print(f"❌ {test_name}: ERROR - {e}")

        print(f"\n{'='*50}")
        print("TEST SUMMARY")
        print('='*50)
        print(f"Passed: {passed_tests}/{total_tests}")
        print(f"Failed: {total_tests - passed_tests}/{total_tests}")

        if passed_tests == total_tests:
            print("🎉 All tests passed! Pipeline is working correctly.")
            return True
        else:
            print("❌ Some tests failed. Please check the logs above.")
            return False

def main():
    """
    Main entry point for the test script.

    USAGE:
    python scripts/test_pipeline.py

    EXIT CODES:
    0 - All tests passed
    1 - Some tests failed
    """
    print("🚀 MySQL-Debezium-Kafka-Typesense Pipeline Tester")
    print("=" * 60)

    tester = PipelineTester()

    try:
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()