#!/bin/bash

# Setup Script for MySQL-Debezium-Kafka-Typesense Pipeline
# This script initializes the entire data pipeline system

set -e  # Exit on any error

echo "🚀 Setting up MySQL-Debezium-Kafka-Typesense Pipeline..."

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to wait for a service to be ready
wait_for_service() {
    local service_name=$1
    local host=$2
    local port=$3
    local max_attempts=30
    local attempt=1

    echo "⏳ Waiting for $service_name to be ready..."

    while [ $attempt -le $max_attempts ]; do
        if nc -z $host $port 2>/dev/null; then
            echo "✅ $service_name is ready!"
            return 0
        fi

        echo "   Attempt $attempt/$max_attempts: $service_name not ready yet..."
        sleep 5
        attempt=$((attempt + 1))
    done

    echo "❌ $service_name failed to start within expected time"
    return 1
}

# Check prerequisites
echo "🔍 Checking prerequisites..."

if ! command_exists docker; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command_exists docker-compose; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

if ! command_exists curl; then
    echo "❌ curl is not installed. Please install curl first."
    exit 1
fi

if ! command_exists nc; then
    echo "❌ netcat (nc) is not installed. Please install netcat first."
    exit 1
fi

echo "✅ All prerequisites are installed"

# Clean up any existing containers
echo "🧹 Cleaning up existing containers..."
docker-compose down -v 2>/dev/null || true

# Build and start services
echo "🏗️  Building and starting services..."
docker-compose up -d --build

# Wait for core services to be ready
echo "⏳ Waiting for services to start..."

# Wait for Zookeeper
wait_for_service "Zookeeper" localhost 2181

# Wait for Kafka
wait_for_service "Kafka" localhost 29092

# Wait for MySQL
wait_for_service "MySQL" localhost 3306

# Wait for Typesense
wait_for_service "Typesense" localhost 8108

# Wait for Debezium Connect
wait_for_service "Debezium Connect" localhost 8083

echo "🎯 All core services are running!"

# Configure Debezium MySQL Connector
echo "⚙️  Configuring Debezium MySQL Connector..."

# Wait a bit more for Debezium to fully initialize
sleep 10

# Register the MySQL connector
curl -X POST \
  http://localhost:8083/connectors \
  -H "Content-Type: application/json" \
  -d @debezium/mysql-connector.json

if [ $? -eq 0 ]; then
    echo "✅ Debezium MySQL Connector configured successfully"
else
    echo "❌ Failed to configure Debezium MySQL Connector"
    echo "   You can manually configure it later using:"
    echo "   curl -X POST http://localhost:8083/connectors -H 'Content-Type: application/json' -d @debezium/mysql-connector.json"
fi

# Check connector status
echo "🔍 Checking connector status..."
sleep 5
curl -s http://localhost:8083/connectors/mysql-connector/status | python3 -m json.tool || echo "Connector status check failed"

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📊 Service Status:"
echo "   - MySQL: http://localhost:3306"
echo "   - Kafka: http://localhost:29092"
echo "   - Debezium Connect: http://localhost:8083"
echo "   - Typesense: http://localhost:8108"
echo ""
echo "🧪 Next steps:"
echo "   1. Run tests: python scripts/test_pipeline.py"
echo "   2. Check logs: docker-compose logs -f"
echo "   3. Monitor services: docker-compose ps"
echo ""
echo "📚 Useful commands:"
echo "   - View Kafka topics: docker exec kafka kafka-topics --bootstrap-server localhost:9092 --list"
echo "   - Check Typesense collections: curl http://localhost:8108/collections -H 'X-TYPESENSE-API-KEY: xyz123'"
echo "   - View connector status: curl http://localhost:8083/connectors/mysql-connector/status"