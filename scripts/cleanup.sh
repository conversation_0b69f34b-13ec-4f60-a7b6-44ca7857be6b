#!/bin/bash

# Cleanup Script for MySQL-Debezium-Kafka-Typesense Pipeline
# This script safely shuts down and cleans up all pipeline resources

set -e  # Exit on any error

echo "🧹 Cleaning up MySQL-Debezium-Kafka-Typesense Pipeline..."

# Function to check if docker-compose is available
check_docker_compose() {
    if ! command -v docker-compose >/dev/null 2>&1; then
        echo "❌ Docker Compose is not installed or not in PATH"
        exit 1
    fi
}

# Function to stop and remove containers
cleanup_containers() {
    echo "🛑 Stopping and removing containers..."

    # Stop all services gracefully
    docker-compose down

    # Remove containers, networks, and volumes
    docker-compose down -v --remove-orphans

    echo "✅ Containers stopped and removed"
}

# Function to clean up Docker images (optional)
cleanup_images() {
    echo "🗑️  Cleaning up Docker images..."

    # Remove dangling images
    docker image prune -f

    # Optionally remove project-specific images
    read -p "Do you want to remove project Docker images? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # Remove images built by this project
        docker images | grep -E "(untitleddddd|python-consumer)" | awk '{print $3}' | xargs -r docker rmi -f
        echo "✅ Project images removed"
    else
        echo "ℹ️  Project images kept"
    fi
}

# Function to clean up volumes
cleanup_volumes() {
    echo "💾 Cleaning up Docker volumes..."

    # Remove unused volumes
    docker volume prune -f

    echo "✅ Unused volumes removed"
}

# Function to clean up networks
cleanup_networks() {
    echo "🌐 Cleaning up Docker networks..."

    # Remove unused networks
    docker network prune -f

    echo "✅ Unused networks removed"
}

# Function to show cleanup summary
show_summary() {
    echo ""
    echo "📊 Cleanup Summary:"
    echo "   - Containers: Stopped and removed"
    echo "   - Volumes: Cleaned up"
    echo "   - Networks: Cleaned up"
    echo "   - Images: Pruned (project images optional)"
    echo ""
    echo "✅ Pipeline cleanup completed successfully!"
    echo ""
    echo "💡 To restart the pipeline:"
    echo "   ./scripts/setup.sh"
    echo "   docker-compose up -d"
}

# Main cleanup process
main() {
    echo "This will stop and remove all pipeline containers, volumes, and networks."
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo

    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Cleanup cancelled"
        exit 0
    fi

    check_docker_compose
    cleanup_containers
    cleanup_volumes
    cleanup_networks
    cleanup_images
    show_summary
}

# Handle script arguments
case "${1:-}" in
    --force)
        echo "🚨 Force cleanup mode - no confirmations"
        check_docker_compose
        cleanup_containers
        cleanup_volumes
        cleanup_networks
        docker image prune -f
        show_summary
        ;;
    --help)
        echo "Usage: $0 [--force|--help]"
        echo ""
        echo "Options:"
        echo "  --force    Skip confirmation prompts"
        echo "  --help     Show this help message"
        echo ""
        echo "This script cleans up the MySQL-Debezium-Kafka-Typesense pipeline by:"
        echo "  - Stopping and removing all containers"
        echo "  - Removing associated volumes and networks"
        echo "  - Optionally removing Docker images"
        ;;
    *)
        main
        ;;
esac