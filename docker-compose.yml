services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - pipeline-network

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: kafka
    depends_on:
      - zookeeper
    ports:
      - "29092:29092"
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
    networks:
      - pipeline-network

  mysql:
    image: mysql:8.0
    container_name: mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: testdb
      MYSQL_USER: dbuser
      MYSQL_PASSWORD: dbpassword
    volumes:
      - ./mysql/my.cnf:/etc/mysql/conf.d/my.cnf
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    command:
      - --server-id=1
      - --log-bin=mysql-bin
      - --binlog-format=ROW
      - --binlog-row-image=FULL
      - --expire-logs-days=10
      - --gtid-mode=ON
      - --enforce-gtid-consistency=ON
    networks:
      - pipeline-network

  typesense:
    image: typesense/typesense:0.25.1
    container_name: typesense
    ports:
      - "8108:8108"
    environment:
      TYPESENSE_DATA_DIR: /data
      TYPESENSE_API_KEY: xyz123
      TYPESENSE_ENABLE_CORS: "true"
    volumes:
      - typesense-data:/data
    networks:
      - pipeline-network

  python-consumer:
    build:
      context: ./python
      dockerfile: Dockerfile
    container_name: python-consumer
    depends_on:
      - kafka
      - typesense
      - mysql
    environment:
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      TYPESENSE_HOST: typesense
      TYPESENSE_PORT: 8108
      TYPESENSE_API_KEY: xyz123
    volumes:
      - ./python:/app
    networks:
      - pipeline-network
    restart: unless-stopped

volumes:
  typesense-data:

networks:
  pipeline-network:
    driver: bridge
