version: '3.8'

# This docker-compose file orchestrates our entire data pipeline
# Each service has specific roles and dependencies explained below

services:
  # ZOOKEEPER SERVICE
  # Purpose: Kafka requires Zookeeper for cluster coordination and metadata management
  # Why needed: Kafka brokers register themselves with <PERSON>keeper, and it manages partition leadership
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    hostname: zookeeper
    container_name: zookeeper
    ports:
      - "2181:2181"  # Standard Zookeeper client port
    environment:
      # ZOOKEEPER_CLIENT_PORT: Port for client connections
      ZOOKEEPER_CLIENT_PORT: 2181
      # ZOOKEEPER_TICK_TIME: Basic time unit in milliseconds used by Zookeeper
      Z<PERSON>OKEEPER_TICK_TIME: 2000
    networks:
      - pipeline-network

  # KAFKA BROKER SERVICE
  # Purpose: Message broker that receives change events from De<PERSON><PERSON><PERSON> and delivers to consumers
  # Why needed: Provides reliable, scalable messaging between Debezium and our Python consumer
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    hostname: kafka
    container_name: kafka
    depends_on:
      - zookeeper  # <PERSON><PERSON><PERSON> needs Zookeeper to be running first
    ports:
      - "29092:29092"  # External access port
      - "9092:9092"    # Internal broker port
    environment:
      # KAFKA_BROKER_ID: Unique identifier for this Kafka broker
      KAFKA_BROKER_ID: 1
      # KAFKA_ZOOKEEPER_CONNECT: Connection string to Zookeeper
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      # KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: Security protocols for different listeners
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      # KAFKA_ADVERTISED_LISTENERS: How clients should connect to Kafka
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      # KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: Replication factor for offset topic
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      # KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: Delay before first rebalance
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      # KAFKA_CONFLUENT_SCHEMA_REGISTRY_URL: Schema registry URL (not used but required)
      KAFKA_CONFLUENT_SCHEMA_REGISTRY_URL: http://schema-registry:8081
      # KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: Minimum in-sync replicas for transaction log
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      # KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: Replication factor for transaction log
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
    networks:
      - pipeline-network

  # MYSQL DATABASE SERVICE
  # Purpose: Source database where our application data lives
  # Why needed: This is the primary data store that Debezium monitors for changes
  mysql:
    image: mysql:8.0
    hostname: mysql
    container_name: mysql
    ports:
      - "3306:3306"  # Standard MySQL port
    environment:
      # MYSQL_ROOT_PASSWORD: Root user password
      MYSQL_ROOT_PASSWORD: rootpassword
      # MYSQL_DATABASE: Database to create on startup
      MYSQL_DATABASE: testdb
      # MYSQL_USER: Non-root user to create
      MYSQL_USER: dbuser
      # MYSQL_PASSWORD: Password for the non-root user
      MYSQL_PASSWORD: dbpassword
    volumes:
      # Mount custom MySQL configuration for CDC (Change Data Capture)
      - ./mysql/my.cnf:/etc/mysql/conf.d/my.cnf
      # Mount initialization script to create tables and sample data
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    command:
      # --server-id: Unique server identifier required for replication
      - --server-id=1
      # --log-bin: Enable binary logging (required for Debezium)
      - --log-bin=mysql-bin
      # --binlog-format: Set binary log format to ROW (required for Debezium)
      - --binlog-format=ROW
      # --binlog-row-image: Log all columns for UPDATE operations
      - --binlog-row-image=FULL
      # --expire-logs-days: Automatically purge binary logs after 10 days
      - --expire-logs-days=10
      # --slave-skip-errors: Skip certain errors during replication
      - --slave-skip-errors=1062
    networks:
      - pipeline-network

  # DEBEZIUM CONNECT SERVICE
  # Purpose: Captures database changes and publishes them to Kafka topics
  # Why needed: Provides CDC (Change Data Capture) functionality to stream DB changes
  debezium:
    image: debezium/connect:2.4
    hostname: debezium
    container_name: debezium
    depends_on:
      - kafka
      - mysql  # Debezium needs both Kafka and MySQL to be running
    ports:
      - "8083:8083"  # Kafka Connect REST API port
    environment:
      # BOOTSTRAP_SERVERS: Kafka broker connection string
      BOOTSTRAP_SERVERS: kafka:9092
      # GROUP_ID: Consumer group ID for Kafka Connect
      GROUP_ID: 1
      # CONFIG_STORAGE_TOPIC: Topic to store connector configurations
      CONFIG_STORAGE_TOPIC: my_connect_configs
      # OFFSET_STORAGE_TOPIC: Topic to store connector offsets
      OFFSET_STORAGE_TOPIC: my_connect_offsets
      # STATUS_STORAGE_TOPIC: Topic to store connector status
      STATUS_STORAGE_TOPIC: my_connect_statuses
      # KEY_CONVERTER: Converter for message keys
      KEY_CONVERTER: org.apache.kafka.connect.json.JsonConverter
      # VALUE_CONVERTER: Converter for message values
      VALUE_CONVERTER: org.apache.kafka.connect.json.JsonConverter
      # CONNECT_KEY_CONVERTER_SCHEMAS_ENABLE: Disable schema in key
      CONNECT_KEY_CONVERTER_SCHEMAS_ENABLE: "false"
      # CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE: Disable schema in value
      CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE: "false"
    networks:
      - pipeline-network

  # TYPESENSE SEARCH ENGINE SERVICE
  # Purpose: Fast, typo-tolerant search engine for our application data
  # Why needed: Provides real-time search capabilities with data synced from MySQL
  typesense:
    image: typesense/typesense:0.25.1
    hostname: typesense
    container_name: typesense
    ports:
      - "8108:8108"  # Typesense API port
    environment:
      # TYPESENSE_DATA_DIR: Directory where Typesense stores data
      TYPESENSE_DATA_DIR: /data
      # TYPESENSE_API_KEY: API key for authentication
      TYPESENSE_API_KEY: xyz123
      # TYPESENSE_ENABLE_CORS: Enable CORS for web applications
      TYPESENSE_ENABLE_CORS: "true"
    volumes:
      # Persist Typesense data
      - typesense-data:/data
    networks:
      - pipeline-network

  # PYTHON CONSUMER SERVICE
  # Purpose: Consumes Kafka messages and syncs data to Typesense
  # Why needed: Acts as the bridge between Kafka (Debezium messages) and Typesense
  python-consumer:
    build:
      context: ./python
      dockerfile: Dockerfile
    hostname: python-consumer
    container_name: python-consumer
    depends_on:
      - kafka
      - typesense
      - mysql  # Consumer needs all these services to be running
    environment:
      # KAFKA_BOOTSTRAP_SERVERS: Kafka connection string
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      # TYPESENSE_HOST: Typesense server host
      TYPESENSE_HOST: typesense
      # TYPESENSE_PORT: Typesense server port
      TYPESENSE_PORT: 8108
      # TYPESENSE_API_KEY: API key for Typesense authentication
      TYPESENSE_API_KEY: xyz123
      # MYSQL_HOST: MySQL server host
      MYSQL_HOST: mysql
      # MYSQL_PORT: MySQL server port
      MYSQL_PORT: 3306
      # MYSQL_USER: MySQL username
      MYSQL_USER: dbuser
      # MYSQL_PASSWORD: MySQL password
      MYSQL_PASSWORD: dbpassword
      # MYSQL_DATABASE: MySQL database name
      MYSQL_DATABASE: testdb
    volumes:
      # Mount Python source code for development
      - ./python:/app
    networks:
      - pipeline-network
    restart: unless-stopped  # Restart if container fails

# DOCKER VOLUMES
# Purpose: Persist data across container restarts
volumes:
  typesense-data:  # Stores Typesense search index data

# DOCKER NETWORKS
# Purpose: Allows containers to communicate with each other
networks:
  pipeline-network:
    driver: bridge  # Default bridge network for container communication