version: '3.8'

services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    hostname: zookeeper
    container_name: zookeeper
    ports:
      - "2181:2181"  
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - pipeline-network

 
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    hostname: kafka
    container_name: kafka
    depends_on:
      - zookeeper 
    ports:
      - "29092:29092"  
      - "9092:9092"   
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      # KAFKA_CONFLUENT_SCHEMA_REGISTRY_URL: Schema registry URL (not used but required)
      KAFKA_CONFLUENT_SCHEMA_REGISTRY_URL: http://schema-registry:8081
      # KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: Minimum in-sync replicas for transaction log
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      # KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: Replication factor for transaction log
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
    networks:
      - pipeline-network


  mysql:
    image: mysql:8.0
    hostname: mysql
    container_name: mysql
    ports:
      - "3306:3306"  # Standard MySQL port
    environment:
      # MYSQL_ROOT_PASSWORD: Root user password
      MYSQL_ROOT_PASSWORD: rootpassword
      # MYSQL_DATABASE: Database to create on startup
      MYSQL_DATABASE: testdb
      # MYSQL_USER: Non-root user to create
      MYSQL_USER: dbuser
      # MYSQL_PASSWORD: Password for the non-root user
      MYSQL_PASSWORD: dbpassword
    volumes:
      # Mount custom MySQL configuration for CDC (Change Data Capture)
      - ./mysql/my.cnf:/etc/mysql/conf.d/my.cnf
      # Mount initialization script to create tables and sample data
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    command:
      # --server-id: Unique server identifier required for replication
      - --server-id=1
      # --log-bin: Enable binary logging (required for Debezium)
      - --log-bin=mysql-bin
      # --binlog-format: Set binary log format to ROW (required for Debezium)
      - --binlog-format=ROW
      # --binlog-row-image: Log all columns for UPDATE operations
      - --binlog-row-image=FULL
      # --expire-logs-days: Automatically purge binary logs after 10 days
      - --expire-logs-days=10
      # --slave-skip-errors: Skip certain errors during replication
      - --slave-skip-errors=1062
    networks:
      - pipeline-network

 
  typesense:
    image: typesense/typesense:0.25.1
    hostname: typesense
    container_name: typesense
    ports:
      - "8108:8108"  # Typesense API port
    environment:
      # TYPESENSE_DATA_DIR: Directory where Typesense stores data
      TYPESENSE_DATA_DIR: /data
      # TYPESENSE_API_KEY: API key for authentication
      TYPESENSE_API_KEY: xyz123
      # TYPESENSE_ENABLE_CORS: Enable CORS for web applications
      TYPESENSE_ENABLE_CORS: "true"
    volumes:
      # Persist Typesense data
      - typesense-data:/data
    networks:
      - pipeline-network


  python-consumer:
    build:
      context: ./python
      dockerfile: Dockerfile
    hostname: python-consumer
    container_name: python-consumer
    depends_on:
      - kafka
      - typesense
      - mysql  # Consumer needs all these services to be running
    environment:
      # KAFKA_BOOTSTRAP_SERVERS: Kafka connection string
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      # TYPESENSE_HOST: Typesense server host
      TYPESENSE_HOST: typesense
      # TYPESENSE_PORT: Typesense server port
      TYPESENSE_PORT: 8108
      # TYPESENSE_API_KEY: API key for Typesense authentication
      TYPESENSE_API_KEY: xyz123
      # MYSQL_HOST: MySQL server host
      MYSQL_HOST: mysql
      # MYSQL_PORT: MySQL server port
      MYSQL_PORT: 3306
      # MYSQL_USER: MySQL username
      MYSQL_USER: dbuser
      # MYSQL_PASSWORD: MySQL password
      MYSQL_PASSWORD: dbpassword
      # MYSQL_DATABASE: MySQL database name
      MYSQL_DATABASE: testdb
    volumes:
      # Mount Python source code for development
      - ./python:/app
    networks:
      - pipeline-network
    restart: unless-stopped  # Restart if container fails


volumes:
  typesense-data:  


networks:
  pipeline-network:
    driver: bridge  