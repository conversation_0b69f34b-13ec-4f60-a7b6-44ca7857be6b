# MySQL Configuration for Change Data Capture (CDC)
# This configuration is ESSENTIAL for <PERSON><PERSON><PERSON><PERSON> to work properly

[mysqld]
# SERVER IDENTIFICATION
# server-id: Unique identifier for this MySQL server in replication setup
# Why needed: Debezium requires this for tracking changes in binary logs
server-id = 1

# BINARY LOGGING CONFIGURATION
# log-bin: Enable binary logging - records all changes to database
# Why needed: De<PERSON>zi<PERSON> reads from binary logs to capture changes
log-bin = mysql-bin

# binlog-format: Format of binary log entries
# ROW format logs actual row changes (required for De<PERSON>zium)
# Why needed: STATEMENT format only logs SQL statements, ROW logs actual data changes
binlog-format = ROW

# binlog-row-image: What columns to log for UPDATE operations
# FULL logs all columns (before and after values)
# Why needed: Debezium needs complete row information to generate proper change events
binlog-row-image = FULL

# BINARY LOG RETENTION
# expire-logs-days: Automatically remove binary logs older than this many days
# Why needed: Prevents disk space issues from accumulating binary logs
expire-logs-days = 10

# binlog-expire-logs-seconds: Alternative to expire-logs-days (in seconds)
# Why needed: More precise control over log retention
binlog-expire-logs-seconds = 864000

# GTID CONFIGURATION (Global Transaction Identifiers)
# gtid-mode: Enable GTID for better replication tracking
# Why needed: Provides unique identifiers for transactions across servers
gtid-mode = ON

# enforce-gtid-consistency: Ensure GTID consistency
# Why needed: Prevents operations that would break GTID consistency
enforce-gtid-consistency = ON

# REPLICATION SETTINGS
# log-slave-updates: Log updates received from master to binary log
# Why needed: Required when this server might become a master
log-slave-updates = ON

# PERFORMANCE SETTINGS
# max-binlog-size: Maximum size of binary log files
# Why needed: Controls when to rotate to new binary log file
max-binlog-size = 100M

# sync-binlog: Sync binary log to disk frequency
# 1 = sync after every transaction (safest but slower)
# Why needed: Ensures durability of binary log entries
sync-binlog = 1

# CHARACTER SET
# default-character-set: Default character set for the server
default-character-set = utf8mb4

# NETWORKING
# bind-address: IP address to bind to (0.0.0.0 allows all connections)
bind-address = 0.0.0.0