-- MySQL Database Initialization Script
-- This script creates tables and sample data for our CDC pipeline demo

-- Use the testdb database (created by docker-compose environment variables)
USE testdb;

-- Create users table
-- This table will demonstrate INSERT, UPDATE, DELETE operations
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    full_name VARCHAR(100) NOT NULL,
    age INT,
    city VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create products table
-- This table will demonstrate e-commerce-like data for search
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    category VARCHAR(50) NOT NULL,
    stock_quantity INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create orders table
-- This table demonstrates relationships and complex data changes
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    total_amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'confirmed', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
    order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- Insert sample users
-- These records will be immediately captured by Debezium when container starts
INSERT INTO users (username, email, full_name, age, city) VALUES
('john_doe', '<EMAIL>', 'John Doe', 28, 'New York'),
('jane_smith', '<EMAIL>', 'Jane Smith', 32, 'Los Angeles'),
('bob_wilson', '<EMAIL>', 'Bob Wilson', 25, 'Chicago'),
('alice_brown', '<EMAIL>', 'Alice Brown', 29, 'Houston'),
('charlie_davis', '<EMAIL>', 'Charlie Davis', 35, 'Phoenix');

-- Insert sample products
-- These will be synced to Typesense for search functionality
INSERT INTO products (name, description, price, category, stock_quantity) VALUES
('Laptop Pro 15', 'High-performance laptop with 16GB RAM and 512GB SSD', 1299.99, 'Electronics', 50),
('Wireless Headphones', 'Noise-cancelling Bluetooth headphones with 30-hour battery', 199.99, 'Electronics', 100),
('Coffee Maker', 'Programmable drip coffee maker with thermal carafe', 89.99, 'Appliances', 25),
('Running Shoes', 'Lightweight running shoes with advanced cushioning', 129.99, 'Sports', 75),
('Smartphone X', 'Latest smartphone with 128GB storage and triple camera', 899.99, 'Electronics', 30),
('Yoga Mat', 'Non-slip yoga mat with carrying strap', 29.99, 'Sports', 200),
('Blender Pro', 'High-speed blender for smoothies and food processing', 149.99, 'Appliances', 40),
('Desk Chair', 'Ergonomic office chair with lumbar support', 249.99, 'Furniture', 15),
('Tablet 10', '10-inch tablet with stylus support', 399.99, 'Electronics', 60),
('Water Bottle', 'Insulated stainless steel water bottle', 24.99, 'Sports', 150);

-- Insert sample orders
-- These demonstrate relationships and will show up in change streams
INSERT INTO orders (user_id, product_id, quantity, total_amount, status) VALUES
(1, 1, 1, 1299.99, 'confirmed'),
(2, 2, 2, 399.98, 'shipped'),
(3, 3, 1, 89.99, 'delivered'),
(4, 4, 1, 129.99, 'pending'),
(5, 5, 1, 899.99, 'confirmed'),
(1, 6, 2, 59.98, 'delivered'),
(2, 7, 1, 149.99, 'shipped'),
(3, 8, 1, 249.99, 'pending');

-- Create indexes for better performance
-- These help with query performance and are important for production
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_city ON users(city);
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_name ON products(name);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);

-- Grant necessary privileges for Debezium user
-- Debezium needs specific privileges to read binary logs and table metadata
GRANT SELECT, RELOAD, SHOW DATABASES, REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO 'dbuser'@'%';
FLUSH PRIVILEGES;

-- Show current binary log status
-- This helps verify that binary logging is working correctly
SHOW MASTER STATUS;