FROM debezium/connect:2.4


EXPOSE 8083


ENV BOOTSTRAP_SERVERS=kafka:9092
ENV GROUP_ID=1
ENV CONFIG_STORAGE_TOPIC=my_connect_configs
ENV CONFIG_STORAGE_REPLICATION_FACTOR=1
ENV OFFSET_STORAGE_TOPIC=my_connect_offsets
ENV OFFSET_STORAGE_REPLICATION_FACTOR=1
ENV STATUS_STORAGE_TOPIC=my_connect_statuses
ENV STATUS_STORAGE_REPLICATION_FACTOR=1
ENV CONNECT_KEY_CONVERTER=org.apache.kafka.connect.json.JsonConverter
ENV CONNECT_KEY_CONVERTER_SCHEMAS_ENABLE=false
ENV CONNECT_VALUE_CONVERTER=org.apache.kafka.connect.json.JsonConverter
ENV CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE=false
ENV CONNECT_REST_ADVERTISED_HOST_NAME=localhost
ENV CONNECT_LOG4J_ROOT_LOGLEVEL=INFO
ENV CONNECT_PLUGIN_PATH=/kafka/connect


HEALTHCHECK --interval=30s --timeout=10s --retries=5 \
  CMD curl -f http://localhost:8083/connectors || exit 1
