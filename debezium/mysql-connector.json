{"name": "mysql-connector", "config": {"connector.class": "io.debezium.connector.mysql.MySqlConnector", "tasks.max": "1", "database.hostname": "mysql", "database.port": "3306", "database.user": "dbuser", "database.password": "dbpassword", "database.server.id": "184054", "topic.prefix": "dbserver1", "database.include.list": "testdb", "schema.history.internal.kafka.bootstrap.servers": "kafka:9092", "schema.history.internal.kafka.topic": "schema-changes.testdb", "include.schema.changes": "true", "table.include.list": "testdb.users,testdb.products,testdb.orders", "transforms": "route", "transforms.route.type": "org.apache.kafka.connect.transforms.RegexRouter", "transforms.route.regex": "([^.]+)\\.([^.]+)\\.([^.]+)", "transforms.route.replacement": "$3", "snapshot.mode": "initial", "binlog.buffer.size": "8192", "heartbeat.interval.ms": "30000", "key.converter": "org.apache.kafka.connect.json.JsonConverter", "key.converter.schemas.enable": "false", "value.converter": "org.apache.kafka.connect.json.JsonConverter", "value.converter.schemas.enable": "false", "decimal.handling.mode": "string", "time.precision.mode": "connect"}}