{"name": "production-mysql-connector", "config": {"connector.class": "io.debezium.connector.mysql.MySqlConnector", "tasks.max": "4", "database.hostname": "PRODUCTION_MYSQL_HOST", "database.port": "PRODUCTION_MYSQL_PORT", "database.user": "PRODUCTION_DB_USER", "database.password": "PRODUCTION_DB_PASSWORD", "database.server.id": "UNIQUE_SERVER_ID", "topic.prefix": "production", "database.include.list": "PRODUCTION_DATABASE_NAMES", "schema.history.internal.kafka.bootstrap.servers": "kafka:9092", "schema.history.internal.kafka.topic": "schema-changes.production", "include.schema.changes": "true", "table.include.list": "PRODUCTION_TABLE_LIST", "transforms": "route", "transforms.route.type": "org.apache.kafka.connect.transforms.RegexRouter", "transforms.route.regex": "([^.]+)\\.([^.]+)\\.([^.]+)", "transforms.route.replacement": "$3", "snapshot.mode": "when_needed", "binlog.buffer.size": "32768", "heartbeat.interval.ms": "10000", "max.batch.size": "4096", "max.queue.size": "16384", "poll.interval.ms": "500", "snapshot.fetch.size": "20480", "incremental.snapshot.chunk.size": "2048", "key.converter": "org.apache.kafka.connect.json.JsonConverter", "key.converter.schemas.enable": "false", "value.converter": "org.apache.kafka.connect.json.JsonConverter", "value.converter.schemas.enable": "false", "decimal.handling.mode": "string", "time.precision.mode": "connect", "database.ssl.mode": "PRODUCTION_SSL_MODE", "tombstones.on.delete": "true", "skipped.operations": "none"}}