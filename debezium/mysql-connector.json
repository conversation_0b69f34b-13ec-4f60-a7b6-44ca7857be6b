{"_comment": "Debezium MySQL Connector Configuration", "_description": "This configuration tells Debezium how to connect to MySQL and capture changes", "name": "mysql-connector", "config": {"_comment_connector_class": "Specifies the MySQL connector class - this is the core Debezium MySQL connector", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "_comment_tasks_max": "Maximum number of tasks for this connector (1 is sufficient for single MySQL instance)", "tasks.max": "1", "_comment_database_connection": "MySQL connection parameters - must match docker-compose MySQL service", "database.hostname": "mysql", "database.port": "3306", "database.user": "dbuser", "database.password": "dbpassword", "_comment_server_id": "Unique server ID for this connector (must be unique in replication topology)", "database.server.id": "184054", "_comment_topic_prefix": "Prefix for Kafka topics created by this connector", "topic.prefix": "dbserver1", "_comment_database_filter": "Only capture changes from 'testdb' database", "database.include.list": "testdb", "_comment_schema_history": "Kafka configuration for storing database schema history", "schema.history.internal.kafka.bootstrap.servers": "kafka:9092", "schema.history.internal.kafka.topic": "schema-changes.testdb", "_comment_schema_changes": "Include schema change events in the stream", "include.schema.changes": "true", "_comment_table_filter": "Only capture changes from these specific tables", "table.include.list": "testdb.users,testdb.products,testdb.orders", "_comment_transforms": "Transform topic names to be cleaner (removes database prefix)", "transforms": "route", "transforms.route.type": "org.apache.kafka.connect.transforms.RegexRouter", "transforms.route.regex": "([^.]+)\\.([^.]+)\\.([^.]+)", "transforms.route.replacement": "$3", "_comment_snapshot": "Configuration for initial snapshot behavior", "snapshot.mode": "initial", "_comment_binlog": "Binary log reading configuration", "binlog.buffer.size": "8192", "_comment_heartbeat": "Send heartbeat messages every 30 seconds to keep connection alive", "heartbeat.interval.ms": "30000", "_comment_key_converter": "How to serialize message keys", "key.converter": "org.apache.kafka.connect.json.JsonConverter", "key.converter.schemas.enable": "false", "_comment_value_converter": "How to serialize message values", "value.converter": "org.apache.kafka.connect.json.JsonConverter", "value.converter.schemas.enable": "false", "_comment_decimal_handling": "How to handle DECIMAL columns (string avoids precision issues)", "decimal.handling.mode": "string", "_comment_time_precision": "Precision for time-based columns", "time.precision.mode": "connect"}}