#!/bin/bash

# Production Deployment Script for MySQL-Debezium-Kafka-Typesense Pipeline
# Run this script after updating all production configurations

set -e

echo "🚀 Starting Production Deployment..."

# Validate required environment variables
required_vars=(
    "PROD_MYSQL_HOST"
    "PROD_MYSQL_PORT" 
    "PROD_DB_USER"
    "PROD_DB_PASSWORD"
    "PROD_SERVER_ID"
    "PROD_DATABASE_LIST"
    "PROD_TABLE_LIST"
)

for var in "${required_vars[@]}"; do
    if [[ -z "${!var}" ]]; then
        echo "❌ Error: Environment variable $var is not set"
        exit 1
    fi
done

echo "✅ Environment variables validated"

# Test database connectivity
echo "🔍 Testing database connectivity..."
mysql -h"$PROD_MYSQL_HOST" -P"$PROD_MYSQL_PORT" -u"$PROD_DB_USER" -p"$PROD_DB_PASSWORD" -e "SELECT 1;" || {
    echo "❌ Database connection failed"
    exit 1
}
echo "✅ Database connection successful"

# Validate binary logging
echo "🔍 Validating MySQL binary logging..."
BINLOG_STATUS=$(mysql -h"$PROD_MYSQL_HOST" -P"$PROD_MYSQL_PORT" -u"$PROD_DB_USER" -p"$PROD_DB_PASSWORD" -se "SHOW VARIABLES LIKE 'log_bin';" | awk '{print $2}')
if [[ "$BINLOG_STATUS" != "ON" ]]; then
    echo "❌ Binary logging is not enabled on MySQL server"
    exit 1
fi
echo "✅ Binary logging is enabled"

# Update Debezium configuration with production values
echo "🔧 Updating Debezium configuration..."
sed -i.bak \
    -e "s/PRODUCTION_MYSQL_HOST/$PROD_MYSQL_HOST/g" \
    -e "s/PRODUCTION_MYSQL_PORT/$PROD_MYSQL_PORT/g" \
    -e "s/PRODUCTION_DB_USER/$PROD_DB_USER/g" \
    -e "s/PRODUCTION_DB_PASSWORD/$PROD_DB_PASSWORD/g" \
    -e "s/UNIQUE_SERVER_ID/$PROD_SERVER_ID/g" \
    -e "s/PRODUCTION_DATABASE_NAMES/$PROD_DATABASE_LIST/g" \
    -e "s/PRODUCTION_TABLE_LIST/$PROD_TABLE_LIST/g" \
    debezium/mysql-connector.json

echo "✅ Debezium configuration updated"

# Start services
echo "🚀 Starting services..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 30

# Start Debezium
echo "🔧 Starting Debezium..."
docker build -f Dockerfile.debezium -t debezium-production .
docker run -d --name debezium-production --network untitledddddfolder_pipeline-network -p 8083:8083 debezium-production

# Wait for Debezium to be ready
echo "⏳ Waiting for Debezium to start..."
sleep 30

# Configure Debezium connector
echo "🔗 Configuring Debezium connector..."
curl -X POST http://localhost:8083/connectors \
    -H "Content-Type: application/json" \
    -d @debezium/mysql-connector.json

echo "✅ Production deployment completed!"
echo "🔍 Check connector status: curl http://localhost:8083/connectors/production-mysql-connector/status"
